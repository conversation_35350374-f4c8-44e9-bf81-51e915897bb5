package amobi.module.openai.client.internal

import amobi.module.openai.client.*
import amobi.module.openai.client.internal.api.*
import amobi.module.openai.client.internal.http.HttpRequester

/**
 * Implementation of [OpenAI].
 *
 * @param requester http transport layer
 */
internal class <PERSON><PERSON><PERSON><PERSON>(
    private val requester: HttpRequester
) : OpenAI,
    Completions by <PERSON><PERSON><PERSON><PERSON><PERSON>(requester),
    Files by <PERSON><PERSON><PERSON>(requester),
    Edits by <PERSON><PERSON><PERSON><PERSON>(requester),
    Embeddings by <PERSON>bed<PERSON>s<PERSON>pi(requester),
    Models by <PERSON><PERSON>pi(requester),
    Moderations by <PERSON>rations<PERSON><PERSON>(requester),
    FineTunes by FineTunesApi(requester),
    Images by <PERSON><PERSON><PERSON>(requester),
    Chat by <PERSON><PERSON><PERSON><PERSON>(requester),
    Audio by <PERSON><PERSON><PERSON>(requester),
    FineTuning by FineTuningApi(requester),
    Assistants by <PERSON><PERSON><PERSON><PERSON>(requester),
    Threads by <PERSON><PERSON><PERSON>s<PERSON><PERSON>(requester),
    Runs by <PERSON>s<PERSON><PERSON>(requester),
    Messages by <PERSON><PERSON><PERSON><PERSON>(requester),
    VectorStores by VectorStores<PERSON><PERSON>(requester),
    Batch by <PERSON><PERSON><PERSON><PERSON>(requester),
    AutoCloseable by requester
