// Android Jetpack Compose .cursorrules

// Flexibility Notice

// Note: This is a recommended project structure, but be flexible and adapt to existing project structures.
// Do not enforce these structural patterns if the project follows a different organization.
// Focus on maintaining consistency with the existing project architecture while applying Jetpack Compose best practices.

// Project Architecture and Best Practices

const androidJetpackComposeBestPractices = [
    "Adapt to existing project architecture while maintaining clean code principles",
    "Follow Material Design 3 guidelines and components",
    "Implement clean architecture with domain, data, and presentation layers",
    "Use Kotlin coroutines and Flow for asynchronous operations",
    "Implement dependency injection using Hilt",
    "Follow unidirectional data flow with ViewModel and UI State",
    "Use Compose navigation for screen management",
    "Implement proper state hoisting and composition",
];

// Folder Structure
// Note: This is a reference structure. Adapt to the project's existing organization

const projectStructure = `
app/src/main/
      java/com/package/
        data/
          db/
          network/
          pref/
        domain/
          usecases/
          models/
          repository/
        presentation/
          common_components/
          common_dialog/
          common_viewmodel/
          navigation/
          screens_1/
            components/
            Screen1.kt
            VideModel1.kt
          screens_2/
            components/
            Screen2.kt
            VideModel2.kt
        utils/
      res/
        values/
        drawable/
        mipmap/
`;

// Compose UI Guidelines

const composeGuidelines = `
1. Use remember and derivedStateOf appropriately
2. Implement proper recomposition optimization
3. Use proper Compose modifiers ordering
4. Follow composable function naming conventions
5. Implement proper preview annotations
6. Use proper state management with MutableState
7. Implement proper error handling and loading states
8. Use proper theming with MaterialTheme
9. Follow accessibility guidelines
10. Implement proper animation patterns
`;


[*.kt]
# ====================== App Compose Components Guidelines ======================
# Prefer App-prefixed foundation components over basic Compose components
# Enforce using App foundation components
deny = [
    "androidx.compose.foundation.layout.Column",
    "androidx.compose.foundation.layout.Box",
    "androidx.compose.foundation.layout.Row",
    "androidx.compose.material3.Text",
    "androidx.compose.material3.Button",
    "androidx.compose.material3.Icon",
    "androidx.compose.material3.Divider",
    "androidx.compose.material3.Switch",
    "androidx.compose.material3.RadioButton"
    "androidx.compose.material3.Spacer"
]
# Suggestion messages for App components
component_suggestions = [
    "AppColumn should include verticalArrangement and horizontalAlignment parameters",
    "AppBox should include contentAlignment parameter",
    "AppRow should include horizontalArrangement and verticalAlignment parameters",
    "AppText requires text parameter",
    "AppButton requires onClick and text parameters",
    "AppIcon requires imageVector parameter",
    "AppSwitch requires checked and onCheckedChange parameters",
    "AppRadioButton requires selected and onClick parameters",
    "AppSpacer requires modifier parameter or size parameter",
]

prefer_components = {
    "Column": "AppColumn",
    "Box": "AppBox",
    "Row": "AppRow",
    "Text": "AppText",
    "Button": "AppButton",
    "Icon": "AppIcon",
    "Divider": "AppDivider",
    "Switch": "AppSwitch",
    "RadioButton": "AppRadioButton"
    "Spacer": "AppSpacer"
}

AppText:
 - Required: text
 - Optional: modifier, color, fontSize, fontWeight, textAlign, maxLines, overflow, style

AppButton:
 - Required: onClick, text
 - Optional: modifier, enabled, loading, buttonStyle, icon, contentPadding

AppIcon:
 - Required: imageVector
 - Optional: modifier, contentDescription, tint, size

AppDivider:
 - Required: none
 - Optional: modifier, color, thickness, startIndent, endIndent

AppSwitch:
 - Required: checked, onCheckedChange
 - Optional: modifier, enabled, colors

AppSpacer:
 - Required: none
 - Optional: modifier, size

# ====================== Key-Value Storage Rules ======================

[**/**.kt]
# Enforce using constants from PrefConst for keys instead of hardcoded strings
enforce_preference_constants = true

# Warn against hardcoded string keys in PrefAssist methods
deny_patterns = [
    "PrefAssist\\.(get|set)(String|Int|Boolean|Long)\\(\"[^\"]+\"",
]

# Prefer using PrefConst constants for preference keys
prefer_patterns = {
    "PrefAssist\\.(get|set)(String|Int|Boolean|Long)\\(\"([^\"]+)\"": "PrefAssist.$1$2(PrefConst.$3",
}

# Encourage using proper default value handling
suggest = [
    "When using PrefAssist.getString without explicit default, ensure PrefConst.getDefString is properly set up for this key",
    "When using PrefAssist.getInt without explicit default, ensure PrefConst.getDefInt is properly set up for this key",
    "When using PrefAssist.getBoolean without explicit default, ensure PrefConst.getDefBoolean is properly set up for this key",
    "When using PrefAssist.getLong without explicit default, ensure PrefConst.getDefLong is properly set up for this key",
]

# Discourage direct access to SharedPreferences outside of PrefAssist
deny = [
    "context\\.getSharedPreferences\\(",
    "getSharedPreferences\\(",
    "\\.edit\\(\\)\\.putString\\(",
    "\\.edit\\(\\)\\.putInt\\(",
    "\\.edit\\(\\)\\.putBoolean\\(",
    "\\.edit\\(\\)\\.putLong\\(",
]
