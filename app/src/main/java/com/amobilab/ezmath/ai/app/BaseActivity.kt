package com.amobilab.ezmath.ai.app

import amobi.module.common.CommApplication
import amobi.module.common.configs.CommFigs
import amobi.module.common.configs.PrefAssist.initPreviewPreferences
import amobi.module.common.configs.RconfAssist
import amobi.module.common.utils.dlog
import amobi.module.common.views.CommActivity
import amobi.module.compose.theme.AppThemeWrapper
import android.content.pm.ActivityInfo
import android.content.res.Configuration
import android.os.Bundle
import androidx.activity.compose.setContent
import androidx.activity.enableEdgeToEdge
import androidx.compose.runtime.Composable
import androidx.compose.runtime.livedata.observeAsState
import com.amobilab.ezmath.ai.data.db.AppDatabase
import com.amobilab.ezmath.ai.data.pref.RconfConst
import com.amobilab.ezmath.ai.presentation.common.shared_values.AppThemeMode
import com.powersync.connector.supabase.SupabaseConnector
import javax.inject.Inject

abstract class BaseActivity : CommActivity() {
    @Inject
    lateinit var coinViewModel: CoinViewModel

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)

        if (CommFigs.IS_DEBUG)
            dlog("Compact Screen: ${isScreenCompact()}")

        requestedOrientation = if (isScreenCompact())
            ActivityInfo.SCREEN_ORIENTATION_PORTRAIT else
            ActivityInfo.SCREEN_ORIENTATION_FULL_USER

        initPreviewPreferences(this)

        val db = AppDatabase.getInstance()
        db.init(this, SupabaseConnector())
        val repository = AppRepository(db.getDatabase())

        setContent {
            val appThemeMode = coinViewModel.appThemeMode.observeAsState()

            AppThemeWrapper(
                darkTheme = when (appThemeMode.value) {
                    AppThemeMode.DARK -> true
                    AppThemeMode.LIGHT -> false
                    AppThemeMode.SYSTEM, null -> {
                        val nightModeFlags = CommApplication.appContext.resources
                            .configuration.uiMode and Configuration.UI_MODE_NIGHT_MASK
                        nightModeFlags == Configuration.UI_MODE_NIGHT_YES
                    }

                }
            ) {
                MainContentCompose()
            }
        }


        enableEdgeToEdge()

        if (RconfAssist.getBoolean(RconfConst.IS_HIDE_SYSTEM_NAV_BAR))
            hideNavigationBarUI()
    }

    @Composable
    protected abstract fun MainContentCompose()

    private fun setWindowFlag(bits: Int, on: Boolean) {
        val winParams = window.attributes
        if (on) {
            winParams.flags = winParams.flags or bits
        } else {
            winParams.flags = winParams.flags and bits.inv()
        }
        window.attributes = winParams
    }
}