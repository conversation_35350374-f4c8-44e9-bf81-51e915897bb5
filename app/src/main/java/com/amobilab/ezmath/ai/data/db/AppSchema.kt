package com.amobilab.ezmath.ai.data.db

import com.powersync.db.schema.Column
import com.powersync.db.schema.Index
import com.powersync.db.schema.IndexedColumn
import com.powersync.db.schema.Schema
import com.powersync.db.schema.Table

val AppSchema = Schema(
    listOf(
        Table(
            name = "history_table",
            columns = listOf(
                Column.integer("historyId", true), // Primary key, auto-increment
                Column.integer("timestamp"),
                Column.text("historyName"),
                Column.integer("isFavorite"), // Boolean stored as 0/1
                Column.text("content"),
                Column.blob("imageData"), // ByteArray
                Column.text("questionMode"),
                Column.text("modelAiChat")
            ),
            indexes = listOf(
                Index("history_timestamp", listOf(IndexedColumn.descending("timestamp")))
            )
        ),
        Table(
            name = "chat_table",
            columns = listOf(
                Column.integer("chatListId", true), // Primary key, auto-increment
                Column.integer("historyId"), // Foreign key
                Column.integer("timestamp"),
                Column.text("content"),
                Column.integer("isHuman"), // Boolean stored as 0/1
                Column.integer("isError"), // Boolean stored as 0/1
                Column.blob("imageData"), // ByteArray
                Column.text("botName")
            ),
            indexes = listOf(
                Index("chat_history", listOf(IndexedColumn.ascending("historyId")))
            ),
            foreignKeys = listOf(
                ForeignKey(
                    columns = listOf("historyId"),
                    referenceTable = "history_table",
                    referenceColumns = listOf("historyId"),
                    onDelete = "CASCADE"
                )
            )
        ),
        Table(
            name = "coin_history_table",
            columns = listOf(
                Column.integer("id", true), // Primary key, auto-increment
                Column.text("type"), // Enum stored as text
                Column.integer("amount"),
                Column.integer("date"),
                Column.text("description")
            ),
            indexes = listOf(
                Index("coin_date", listOf(IndexedColumn.descending("date")))
            )
        )
    )
)