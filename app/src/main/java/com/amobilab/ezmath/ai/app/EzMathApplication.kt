/////////////////////////////////////////////////////////
//                                                     //
//                       _oo0oo_                       //
//                      o8888888o                      //
//                      88" . "88                      //
//                      (| -_- |)                      //
//                      0\  =  /0                      //
//                    ___/`---'\___                    //
//                  .' \\|     |// '.                  //
//                 / \\|||  :  |||// \                 //
//                / _||||| -:- |||||- \                //
//               |   | \\\  -  /// |   |               //
//               | \_|  ''\---/''  |_/ |               //
//               \  .-\__  '-'  ___/-. /               //
//             ___'. .'  /--.--\  `. .'___             //
//          ."" '<  `.___\_<|>_/___.' >' "".           //
//         | | :  `- \`.;`\ _ /`;.`/ - ` : | |         //
//         \  \ `_.   \_ __\ /__ _/   .-` /  /         //
//     =====`-.____`.___ \_____/___.-`___.-'=====      //
//                       `=---='                       //
//                                                     //
//                                                     //
//     ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~     //
//                                                     //
//  KHÔNG BUG!     KHÔNG CRASH!   RELEASE THÀNH CÔNG!  //
//                                                     //
//                    A DI ĐÀ PHẬT!                    //
//                                                     //
/////////////////////////////////////////////////////////

package com.amobilab.ezmath.ai.app

import amobi.module.common.CommApplication
import amobi.module.common.advertisements.AdvertsInstance
import amobi.module.common.configs.CommFigs
import amobi.module.common.configs.PrefAssist
import amobi.module.common.configs.RconfAssist
import amobi.module.common.utils.FirebaseAssist
import amobi.module.common.utils.MixedUtils
import android.content.Context
import androidx.multidex.MultiDex
import com.airbnb.lottie.utils.MiscUtils
import com.amobilab.ezmath.ai.BuildConfig
import com.amobilab.ezmath.ai.R
import com.amobilab.ezmath.ai.data.db.AppDatabase
import com.amobilab.ezmath.ai.data.pref.PrefConst
import com.amobilab.ezmath.ai.data.pref.RconfConst
import com.amobilab.ezmath.ai.presentation.common.shared_values.ModelAiMode
import com.amobilab.ezmath.ai.utils.CustomAppCheckProviderFactory
import com.google.firebase.appcheck.FirebaseAppCheck
import com.google.firebase.appcheck.playintegrity.PlayIntegrityAppCheckProviderFactory
import dagger.hilt.android.HiltAndroidApp

@HiltAndroidApp
class EzMathApplication : CommApplication() {

    override fun attachBaseContext(base: Context) {
        super.attachBaseContext(base)
        MultiDex.install(this)
    }

    override fun onCreate() {
        super.onCreate()

        CommFigs.setupBuildConfigs(BuildConfig.DEBUG, BuildConfig.FLAVOR)

        FirebaseAppCheck.getInstance().installAppCheckProviderFactory(
            if (CommFigs.IS_PRODUCT && !CommFigs.IS_DEBUG)
                PlayIntegrityAppCheckProviderFactory.getInstance()
            else
                CustomAppCheckProviderFactory.getInstance(),
        )

        PrefAssist.defaultValueString = PrefConst::getDefString
        PrefAssist.defaultValueBoolean = PrefConst::getDefBoolean
        PrefAssist.defaultValueInt = PrefConst::getDefInt
        PrefAssist.defaultValueLong = PrefConst::getDefLong

        AppDatabase.getInstance().init(this)
        initialize()
    }

    override fun initialize(): Boolean {
        if (!super.initialize())
            return false

        FirebaseAssist.Companion.instance.fetchRemoteConfig(
            R.xml.remote_config_defaults,
            onSuccessFetch = {
                if (CommFigs.IS_SHOW_TEST_OPTION) {
                    MixedUtils.showToast(RconfConst.getAbTestVersion())
                }
            },
            onCompleteFetch = {
                if (RconfAssist.getInt(RconfConst.SHOW_AI_TYPE) == RconfConst.SHOW_AI_GEMINI_TYPE) {
                    PrefAssist.setString(PrefConst.MODEL_AI, ModelAiMode.GEMINI.name)
                } else if (RconfAssist.getInt(RconfConst.SHOW_AI_TYPE) == RconfConst.SHOW_AI_GPT_TYPE) {
                    PrefAssist.setString(PrefConst.MODEL_AI, ModelAiMode.GPT.name)
                }

            }
        )

        AdvertsInstance.Companion.clearLastTimeShowedFullAd()

        return true
    }
}