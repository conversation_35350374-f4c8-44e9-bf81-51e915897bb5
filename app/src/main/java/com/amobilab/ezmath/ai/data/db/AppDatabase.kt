package com.amobilab.ezmath.ai.data.db

import android.content.Context
import com.powersync.DatabaseDriverFactory
import com.powersync.PowerSyncDatabase
import com.powersync.connectors.PowerSyncBackendConnector
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch

class AppDatabase private constructor() {
    private lateinit var db: PowerSyncDatabase

    fun init(context: Context, connector: PowerSyncBackendConnector) {
        if (!::db.isInitialized) {
            val driverFactory = DatabaseDriverFactory(context)
            db = PowerSyncDatabase(
                factory = driverFactory,
                schema = AppSchema,
                dbFilename = "app_db.db"
            )
            // Kết nối với backend
            CoroutineScope(Dispatchers.IO).launch {
                db.connect(connector = connector)
            }
        }
    }

    fun getDatabase(): PowerSyncDatabase = db

    companion object {
        @Volatile
        private var INSTANCE: AppDatabase? = null

        fun getInstance(): AppDatabase {
            return INSTANCE ?: synchronized(this) {
                val instance = AppDatabase()
                INSTANCE = instance
                instance
            }
        }

        const val DB_NAME = "app_db"
    }
}