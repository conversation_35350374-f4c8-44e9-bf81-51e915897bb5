package com.amobilab.ezmath.ai.data.db


import com.powersync.DatabaseDriverFactory
import com.powersync.PowerSyncDatabase
import com.powersync.db.SqlCursor
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.map

data class History(
    val historyId: Int,
    val timestamp: Long,
    val historyName: String,
    val isFavorite: Boolean,
    val content: String,
    val imageData: ByteArray?,
    val questionMode: String,
    val modelAiChat: String
)

data class Chat(
    val chatListId: Int,
    val historyId: Long,
    val timestamp: Long,
    val content: String,
    val isHuman: Boolean,
    val isError: Boolean,
    val imageData: ByteArray?,
    val botName: String?
)

data class CoinHistory(
    val id: Long,
    val type: TransactionType,
    val amount: Int,
    val date: Long,
    val description: String
)

enum class TransactionType {
    EARN, SPEND
}

class AppRepository(private val database: PowerSyncDatabase) {

    // History operations
    suspend fun getHistoryById(historyId: Long): History? {
        return database.getOptional(
            sql = "SELECT * FROM history_table WHERE historyId = ?",
            parameters = listOf(historyId)
        ) { cursor -> mapToHistory(cursor) }
    }

    fun getAllHistory(): Flow<List<History>> {
        return database.watch(
            sql = "SELECT * FROM history_table"
        ) { cursor -> mapToHistory(cursor) }
    }

    suspend fun insertHistory(history: History): Long {
        return database.writeTransaction { tx ->
            tx.execute(
                sql = """
                    INSERT INTO history_table (timestamp, historyName, isFavorite, content, imageData, questionMode, modelAiChat)
                    VALUES (?, ?, ?, ?, ?, ?, ?)
                    RETURNING historyId
                """,
                parameters = listOf(
                    history.timestamp,
                    history.historyName,
                    if (history.isFavorite) 1 else 0,
                    history.content,
                    history.imageData,
                    history.questionMode,
                    history.modelAiChat
                )
            ).lastInsertRowId
        }
    }

    suspend fun updateHistory(history: History) {
        database.execute(
            sql = """
                UPDATE history_table 
                SET timestamp = ?, historyName = ?, isFavorite = ?, content = ?, imageData = ?, questionMode = ?, modelAiChat = ?
                WHERE historyId = ?
            """,
            parameters = listOf(
                history.timestamp,
                history.historyName,
                if (history.isFavorite) 1 else 0,
                history.content,
                history.imageData,
                history.questionMode,
                history.modelAiChat,
                history.historyId
            )
        )
    }

    suspend fun deleteHistory(historyId: Int) {
        database.execute(
            sql = "DELETE FROM history_table WHERE historyId = ?",
            parameters = listOf(historyId)
        )
    }

    suspend fun deleteHistories(historyIds: List<Long>) {
        database.execute(
            sql = "DELETE FROM history_table WHERE historyId IN (${historyIds.joinToString(",") { "?" }})",
            parameters = historyIds
        )
    }

    // Chat operations
    suspend fun getChatsForHistory(historyId: Long): List<Chat> {
        return database.getAll(
            sql = "SELECT * FROM chat_table WHERE historyId = ?",
            parameters = listOf(historyId)
        ) { cursor -> mapToChat(cursor) }
    }

    suspend fun insertChat(chat: Chat) {
        database.writeTransaction { tx ->
            tx.execute(
                sql = """
                    INSERT INTO chat_table (historyId, timestamp, content, isHuman, isError, imageData, botName)
                    VALUES (?, ?, ?, ?, ?, ?, ?)
                """,
                parameters = listOf(
                    chat.historyId,
                    chat.timestamp,
                    chat.content,
                    if (chat.isHuman) 1 else 0,
                    if (chat.isError) 1 else 0,
                    chat.imageData,
                    chat.botName
                )
            )
        }
    }

    suspend fun updateChat(chat: Chat) {
        database.execute(
            sql = """
                UPDATE chat_table 
                SET historyId = ?, timestamp = ?, content = ?, isHuman = ?, isError = ?, imageData = ?, botName = ?
                WHERE chatListId = ?
            """,
            parameters = listOf(
                chat.historyId,
                chat.timestamp,
                chat.content,
                if (chat.isHuman) 1 else 0,
                if (chat.isError) 1 else 0,
                chat.imageData,
                chat.botName,
                chat.chatListId
            )
        )
    }

    suspend fun deleteChatsForHistory(historyId: Long) {
        database.execute(
            sql = "DELETE FROM chat_table WHERE historyId = ?",
            parameters = listOf(historyId)
        )
    }

    suspend fun deleteChatsForHistories(historyIds: List<Long>) {
        database.execute(
            sql = "DELETE FROM chat_table WHERE historyId IN (${historyIds.joinToString(",") { "?" }})",
            parameters = historyIds
        )
    }

    suspend fun getLastChat(historyId: Long): Chat? {
        return database.getOptional(
            sql = "SELECT * FROM chat_table WHERE historyId = ? ORDER BY chatListId DESC LIMIT 1",
            parameters = listOf(historyId)
        ) { cursor -> mapToChat(cursor) }
    }

    // CoinHistory operations
    suspend fun insertTransaction(transaction: CoinHistory) {
        database.writeTransaction { tx ->
            tx.execute(
                sql = """
                    INSERT INTO coin_history_table (type, amount, date, description)
                    VALUES (?, ?, ?, ?)
                """,
                parameters = listOf(
                    transaction.type.name,
                    transaction.amount,
                    transaction.date,
                    transaction.description
                )
            )
        }
    }

    fun getAllTransactions(): Flow<List<CoinHistory>> {
        return database.watch(
            sql = "SELECT * FROM coin_history_table"
        ) { cursor -> mapToCoinHistory(cursor) }
    }

    private fun mapToHistory(cursor: SqlCursor): History {
        return History(
            historyId = cursor.getInt("historyId") ?: 0,
            timestamp = cursor.getLong("timestamp") ?: 0,
            historyName = cursor.getString("historyName") ?: "",
            isFavorite = (cursor.getInt("isFavorite") ?: 0) == 1,
            content = cursor.getString("content") ?: "",
            imageData = cursor.getBlob("imageData"),
            questionMode = cursor.getString("questionMode") ?: "",
            modelAiChat = cursor.getString("modelAiChat") ?: ""
        )
    }

    private fun mapToChat(cursor: ResultRow): Chat {
        return Chat(
            chatListId = cursor.getInt("chatListId") ?: 0,
            historyId = cursor.getLong("historyId") ?: 0,
            timestamp = cursor.getLong("timestamp") ?: 0,
            content = cursor.getString("content") ?: "",
            isHuman = (cursor.getInt("isHuman") ?: 0) == 1,
            isError = (cursor.getInt("isError") ?: 0) == 1,
            imageData = cursor.getBlob("imageData"),
            botName = cursor.getString("botName")
        )
    }

    private fun mapToCoinHistory(cursor: ResultRow): CoinHistory {
        return CoinHistory(
            id = cursor.getLong("id") ?: 0,
            type = TransactionType.valueOf(cursor.getString("type") ?: "EARN"),
            amount = cursor.getInt("amount") ?: 0,
            date = cursor.getLong("date") ?: 0,
            description = cursor.getString("description") ?: ""
        )
    }
}