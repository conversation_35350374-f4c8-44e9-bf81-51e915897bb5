<?xml version='1.0' encoding='utf-8'?>
<resources>
    <string name="generate_outline" translate_by="gpt">Generate Outline</string>
    <string name="placeholder_default" translate_by="gpt">Ex: Enter your text here…</string>
    <string name="essay_screen_description" translate_by="gpt">Sure! I can help with that. Please provide some details for your essay below.</string>
    <string name="label_choose_topic" translate_by="gpt">Choose a topic</string>
    <string name="label_essay_type" translate_by="gpt">Type of essay</string>
    <string name="label_word_count" translate_by="gpt">Word count</string>
    <string name="label_language_tone" translate_by="gemini">Language + tone</string>
    <string name="placeholder_topic" translate_by="gpt">Ex: Describe a place that makes you feel at peace</string>
    <string name="placeholder_essay_type" translate_by="gpt">Ex: Argumentative, Narrative…</string>
    <string name="placeholder_word_count" translate_by="gpt">Ex: 300 words, 500 words, 1000 words…</string>
    <string name="placeholder_language_tone" translate_by="gpt">Ex: Formal, academic, …</string>
    <string name="research_screen_description" translate_by="gpt">A place to turn raw data into meaningful, visual stories through research and analysis.</string>
    <string name="label_research_topic" translate_by="gpt">Research topic</string>
    <string name="label_research_goal" translate_by="gemini">Research goal</string>
    <string name="label_preferred_sources" translate_by="gpt">Preferred sources</string>
    <string name="label_depth_length" translate_by="gpt">Depth / Length</string>
    <string name="label_academic_level" translate_by="gpt">Academic level</string>
    <string name="placeholder_research_topic" translate_by="gpt">Ex: Climate change, The Impact of AI on Jobs, …</string>
    <string name="placeholder_research_goal" translate_by="gpt">Ex: Information Gathering, Trend Analysis…</string>
    <string name="placeholder_preferred_sources" translate_by="gpt">Ex: Scientific journals, books, official articles</string>
    <string name="placeholder_depth_length" translate_by="gpt">Ex: 300 words, 500 words, 1000 words…</string>
    <string name="placeholder_academic_level" translate_by="gpt">Ex: Secondary school students, university students, advanced research, …</string>
    <string name="literature_screen_description" translate_by="gemini">From words to hidden meanings, we help you uncover the true value of every literary work.</string>
    <string name="label_title_of_work" translate_by="gpt">Title of the work</string>
    <string name="label_author" translate_by="gpt">Author</string>
    <string name="label_analysis_type" translate_by="gpt">What do you want to analyse?</string>
    <string name="label_format" translate_by="gpt">Length / format</string>
    <string name="placeholder_title" translate_by="gpt">Ex: The Great Gatsby</string>
    <string name="placeholder_author" translate_by="gpt">Ex: F. Scott Fitzgerald</string>
    <string name="placeholder_analysis_type" translate_by="gpt">Ex: Character analysis, Main themes…</string>
    <string name="placeholder_format" translate_by="gpt">Ex: 300 words, 500 words, 1000 words…</string>
    <string name="placeholder_academic_level_literature" translate_by="gemini">Secondary school, sixth form college or university, …</string>
    <string name="research_outline_topic_label" translate_by="gpt">📘 Research Topic: %1$s</string>
    <string name="research_outline_goal_label" translate_by="gpt">🎯 Research Goal: %1$s</string>
    <string name="research_outline_sources_label" translate_by="gpt">📚 Preferred Sources: %1$s</string>
    <string name="research_outline_depth_label" translate_by="gpt">📏 Depth/Length: %1$s</string>
    <string name="research_outline_academic_level_label" translate_by="gpt">🎓 Academic Level: %1$s</string>
    <string name="research_outline_suggested_outline_label" translate_by="gpt">🧾 Suggested Outline:</string>
    <string name="research_outline_introduction_title" translate_by="gpt">1. Introduction</string>
    <string name="research_outline_introduction_overview" translate_by="gpt">- Brief overview of %1$s</string>
    <string name="research_outline_introduction_importance" translate_by="gpt">- Importance of research at the %1$s level</string>
    <string name="research_outline_objectives_title" translate_by="gpt">2. Objectives</string>
    <string name="research_outline_objectives_goal" translate_by="gpt">- Clarify the primary goal: %1$s</string>
    <string name="research_outline_methodology_title" translate_by="gpt">3. Methodology</string>
    <string name="research_outline_methodology_approach" translate_by="google">- Research approach</string>
    <string name="research_outline_methodology_sources" translate_by="gpt">- Data sources: %1$s</string>
    <string name="research_outline_key_insights_title" translate_by="gpt">4. Key Insights</string>
    <string name="research_outline_key_insights_trends" translate_by="gpt">- Discuss trends, facts, or analysis findings</string>
    <string name="research_outline_key_insights_citations" translate_by="gpt">- Use citations if necessary</string>
    <string name="research_outline_conclusion_title" translate_by="gpt">5. Conclusion</string>
    <string name="research_outline_conclusion_summary" translate_by="gpt">- Summary of findings</string>
    <string name="research_outline_conclusion_implications" translate_by="gpt">- Implications or future work</string>
    <string name="essay_outline_topic_label" translate_by="gpt">✏️ Essay Topic: %1$s</string>
    <string name="essay_outline_type_label" translate_by="gpt">📝 Essay Type: %1$s</string>
    <string name="essay_outline_word_count_label" translate_by="gpt">🔢 Word Count: %1$s</string>
    <string name="essay_outline_language_tone_label" translate_by="gpt">🗣️ Language &amp; Tone: %1$s</string>
    <string name="essay_outline_suggested_outline_label" translate_by="gpt">🧾 Suggested Outline:</string>
    <string name="essay_outline_introduction_title" translate_by="gpt">1. Introduction</string>
    <string name="essay_outline_introduction_topic" translate_by="gpt">- Introduce the topic: %1$s</string>
    <string name="essay_outline_introduction_background" translate_by="gpt">- Provide background/context</string>
    <string name="essay_outline_introduction_thesis" translate_by="gpt">- State the thesis</string>
    <string name="essay_outline_body_title" translate_by="gpt">2. Body Paragraphs</string>
    <string name="essay_outline_body_paragraph1" translate_by="gpt">- Paragraph 1: First argument or point</string>
    <string name="essay_outline_body_paragraph2" translate_by="gpt">- Paragraph 2: Supporting evidence or narrative</string>
    <string name="essay_outline_body_paragraph3" translate_by="gpt">- Paragraph 3: Counter-argument or additional detail</string>
    <string name="essay_outline_conclusion_title" translate_by="gpt">3. Conclusion</string>
    <string name="essay_outline_conclusion_summary" translate_by="gpt">- Summarise key points</string>
    <string name="essay_outline_conclusion_restate" translate_by="google">- Restate thesis in a new way</string>
    <string name="essay_outline_conclusion_final" translate_by="gpt">- Conclude with a strong final thought</string>
    <string name="essay_outline_notes_title" translate_by="gpt">✨ Notes:</string>
    <string name="essay_outline_notes_tone" translate_by="gpt">- Maintain a %1$s tone throughout</string>
    <string name="essay_outline_notes_wordcount" translate_by="gpt">- Aim for approximately %1$s total</string>
    <string name="essay_outline_notes_structure" translate_by="gpt">- Follow typical %1$s essay structure</string>
    <string name="literature_outline_title_label" translate_by="gpt">Title: %1$s</string>
    <string name="literature_outline_author_label" translate_by="gpt">Author: %1$s</string>
    <string name="literature_outline_focus_label" translate_by="gpt">Focus: %1$s</string>
    <string name="literature_outline_length_label" translate_by="gpt">Length: %1$s</string>
    <string name="literature_outline_academic_level_label" translate_by="gpt">Academic Level: %1$s</string>
    <string name="literature_outline_outline_label" translate_by="gpt">Outline:</string>
    <string name="literature_outline_introduction_title" translate_by="gpt">1. Introduction</string>
    <string name="literature_outline_introduction_context" translate_by="gpt">Introduce the literary work and its context.</string>
    <string name="literature_outline_introduction_author" translate_by="gpt">Mention the author and relevance to the chosen analysis focus.</string>
    <string name="literature_outline_background_title" translate_by="gpt">2. Background</string>
    <string name="literature_outline_background_summary" translate_by="gpt">Summary of the plot or key characters (depending on analysis type).</string>
    <string name="literature_outline_background_context" translate_by="gpt">Provide necessary context for deeper analysis.</string>
    <string name="literature_outline_analysis_title" translate_by="gpt">3. Main Analysis</string>
    <string name="literature_outline_analysis_deep_dive" translate_by="gpt">Deep dive into: %1$s.</string>
    <string name="literature_outline_analysis_evidence" translate_by="gpt">Use evidence from the text: quotes, events, symbolism, etc.</string>
    <string name="literature_outline_connections_title" translate_by="gpt">4. Connections</string>
    <string name="literature_outline_connections_themes" translate_by="gpt">Link analysis to larger themes or real-world implications.</string>
    <string name="literature_outline_connections_contrast" translate_by="gpt">Optionally contrast with other characters or works.</string>
    <string name="literature_outline_conclusion_title" translate_by="gpt">5. Conclusion</string>
    <string name="literature_outline_conclusion_insights" translate_by="gpt">Restate key insights.</string>
    <string name="literature_outline_conclusion_value" translate_by="gpt">Reflect on the value of the work from an academic perspective.</string>
</resources>