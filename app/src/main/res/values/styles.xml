<?xml version="1.0" encoding="utf-8"?>
<resources>

    <style name="ShapeAppearanceOverlay_card_receive_corners">
        <item name="cornerFamily">rounded</item>
        <item name="cornerSizeTopRight">8dp</item>
        <item name="cornerSizeTopLeft">0dp</item>
        <item name="cornerSizeBottomRight">8dp</item>
        <item name="cornerSizeBottomLeft">0dp</item>
    </style>

    <style name="CardBubbleReceiveBox" parent="@style/Widget.MaterialComponents.CardView">
        <item name="shapeAppearanceOverlay">@style/ShapeAppearanceOverlay_card_receive_corners</item>
        <item name="cardBackgroundColor">@color/clr_transparent</item>
        <item name="cardElevation">0dp</item>
    </style>

    <style name="ShapeAppearanceOverlay_card_send_corners">
        <item name="cornerFamily">rounded</item>
        <item name="cornerSizeTopRight">0dp</item>
        <item name="cornerSizeTopLeft">8dp</item>
        <item name="cornerSizeBottomRight">0dp</item>
        <item name="cornerSizeBottomLeft">8dp</item>
    </style>
    
    <style name="CardBubbleSentBox" parent="@style/Widget.MaterialComponents.CardView">
        <item name="shapeAppearanceOverlay">@style/ShapeAppearanceOverlay_card_send_corners</item>
        <item name="cardBackgroundColor">@color/clr_transparent</item>
        <item name="cardElevation">0dp</item>
    </style>

    <style name="CircleImageView" parent="">
        <item name="cornerFamily">rounded</item>
        <item name="cornerSize">50%</item>
    </style>
</resources>