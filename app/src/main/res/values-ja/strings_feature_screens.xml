<?xml version='1.0' encoding='utf-8'?>
<resources>
    <string name="generate_outline" translate_by="gemini">アウトラインを作成</string>
    <string name="placeholder_default" translate_by="gpt">テキストを入力してください…</string>
    <string name="essay_screen_description" translate_by="gpt">もちろん！それについてお手伝いできます。以下にエッセイの詳細を提供してください。</string>
    <string name="label_choose_topic" translate_by="gemini">トピックを選択</string>
    <string name="label_essay_type" translate_by="gpt">エッセイの種類</string>
    <string name="label_word_count" translate_by="gpt">単語数</string>
    <string name="label_language_tone" translate_by="gemini">言語 + 口調</string>
    <string name="placeholder_topic" translate_by="gemini">心が安らぐ場所について説明してください。</string>
    <string name="placeholder_essay_type" translate_by="gpt">例：議論的、物語的…</string>
    <string name="placeholder_word_count" translate_by="gpt">例：300語、500語、1000語…</string>
    <string name="placeholder_language_tone" translate_by="gpt">例：フォーマル、学術的、…</string>
    <string name="research_screen_description" translate_by="gemini">調査と分析を通じて、生データを有意義で視覚的なストーリーに変える場所。</string>
    <string name="label_research_topic" translate_by="gpt">研究テーマ</string>
    <string name="label_research_goal" translate_by="gpt">研究目標</string>
    <string name="label_preferred_sources" translate_by="gpt">優先ソース</string>
    <string name="label_depth_length" translate_by="gemini">奥行き / 長さ</string>
    <string name="label_academic_level" translate_by="gpt">学問レベル</string>
    <string name="placeholder_research_topic" translate_by="google">例：気候変動、AIが仕事に与える影響、…</string>
    <string name="placeholder_research_goal" translate_by="google">例：情報収集、トレンド分析…</string>
    <string name="placeholder_preferred_sources" translate_by="gpt">科学雑誌、書籍、公式記事</string>
    <string name="placeholder_depth_length" translate_by="gpt">例：300語、500語、1000語…</string>
    <string name="placeholder_academic_level" translate_by="google">例：高校生、大学生、高度な研究、…</string>
    <string name="literature_screen_description" translate_by="gemini">言葉から隠された意味へ。あらゆる文学作品の真価を解き明かすお手伝いをします。</string>
    <string name="label_title_of_work" translate_by="gpt">作品のタイトル</string>
    <string name="label_author" translate_by="gpt">著者</string>
    <string name="label_analysis_type" translate_by="gpt">何を分析したいですか？</string>
    <string name="label_format" translate_by="gpt">長さ / 形式</string>
    <string name="placeholder_title" translate_by="gpt">グレート・ギャツビー</string>
    <string name="placeholder_author" translate_by="gpt">F・スコット・フィッツジェラルド</string>
    <string name="placeholder_analysis_type" translate_by="gemini">キャラクター分析、主なテーマ…</string>
    <string name="placeholder_format" translate_by="gpt">例：300語、500語、1000語…</string>
    <string name="placeholder_academic_level_literature" translate_by="google">例：中学校、高校、大学、…</string>
    <string name="research_outline_topic_label" translate_by="gpt">📘 研究テーマ: %1$s</string>
    <string name="research_outline_goal_label" translate_by="gpt">🎯 研究目標: %1$s</string>
    <string name="research_outline_sources_label" translate_by="gpt">📚 おすすめのソース: %1$s</string>
    <string name="research_outline_depth_label" translate_by="gpt">📏 深さ/長さ: %1$s</string>
    <string name="research_outline_academic_level_label" translate_by="gpt">🎓 学歴: %1$s</string>
    <string name="research_outline_suggested_outline_label" translate_by="gemini">🧾 おすすめの構成：</string>
    <string name="research_outline_introduction_title" translate_by="gpt">1. はじめに</string>
    <string name="research_outline_introduction_overview" translate_by="gpt">%1$sの簡単な概要</string>
    <string name="research_outline_introduction_importance" translate_by="gemini">%1$s レベルにおける研究の重要性</string>
    <string name="research_outline_objectives_title" translate_by="gpt">2. 目的</string>
    <string name="research_outline_objectives_goal" translate_by="gemini">主な目標を明確にする：%1$s</string>
    <string name="research_outline_methodology_title" translate_by="gpt">3. 方法論</string>
    <string name="research_outline_methodology_approach" translate_by="gpt">研究アプローチ</string>
    <string name="research_outline_methodology_sources" translate_by="gpt">- データソース: %1$s</string>
    <string name="research_outline_key_insights_title" translate_by="gemini">4. 主な洞察</string>
    <string name="research_outline_key_insights_trends" translate_by="gemini">トレンド、事実、分析結果について議論する</string>
    <string name="research_outline_key_insights_citations" translate_by="gemini">必要に応じて引用を使用してください</string>
    <string name="research_outline_conclusion_title" translate_by="gpt">5. 結論</string>
    <string name="research_outline_conclusion_summary" translate_by="gpt">調査結果の概要</string>
    <string name="research_outline_conclusion_implications" translate_by="gpt">- 意義または今後の作業</string>
    <string name="essay_outline_topic_label" translate_by="gemini">✏️ 小論文のテーマ: %1$s</string>
    <string name="essay_outline_type_label" translate_by="gpt">📝 エッセイタイプ: %1$s</string>
    <string name="essay_outline_word_count_label" translate_by="gemini">🔢 ワード数: %1$s</string>
    <string name="essay_outline_language_tone_label" translate_by="gpt">🗣️ 言語とトーン: %1$s</string>
    <string name="essay_outline_suggested_outline_label" translate_by="gemini">🧾 おすすめの構成：</string>
    <string name="essay_outline_introduction_title" translate_by="gpt">1. はじめに</string>
    <string name="essay_outline_introduction_topic" translate_by="gemini">トピックを紹介: %1$s</string>
    <string name="essay_outline_introduction_background" translate_by="gemini">背景/コンテキストを提供</string>
    <string name="essay_outline_introduction_thesis" translate_by="gpt">論文の主張を述べる</string>
    <string name="essay_outline_body_title" translate_by="gpt">2. 本文段落</string>
    <string name="essay_outline_body_paragraph1" translate_by="gpt">- パラグラフ 1: 最初の主張またはポイント</string>
    <string name="essay_outline_body_paragraph2" translate_by="gemini">段落2：裏付けとなる証拠または説明</string>
    <string name="essay_outline_body_paragraph3" translate_by="gemini">第3段落：反論または追加の詳細</string>
    <string name="essay_outline_conclusion_title" translate_by="gpt">3. 結論</string>
    <string name="essay_outline_conclusion_summary" translate_by="gpt">重要なポイントを要約する</string>
    <string name="essay_outline_conclusion_restate" translate_by="gpt">新しい言い方で論文の主張を再表現する</string>
    <string name="essay_outline_conclusion_final" translate_by="gpt">強い最後の考えで締めくくりましょう。</string>
    <string name="essay_outline_notes_title" translate_by="google">✨メモ：</string>
    <string name="essay_outline_notes_tone" translate_by="gpt">- 一貫して%1$sのトーンを維持する</string>
    <string name="essay_outline_notes_wordcount" translate_by="gpt">約%1$s合計を目指してください</string>
    <string name="essay_outline_notes_structure" translate_by="gemini">典型的な%1$sエッセイ構造に従ってください</string>
    <string name="literature_outline_title_label" translate_by="gpt">タイトル: %1$s</string>
    <string name="literature_outline_author_label" translate_by="gpt">著者: %1$s</string>
    <string name="literature_outline_focus_label" translate_by="gemini">フォーカス：%1$s</string>
    <string name="literature_outline_length_label" translate_by="gpt">長さ: %1$s</string>
    <string name="literature_outline_academic_level_label" translate_by="gpt">学年: %1$s</string>
    <string name="literature_outline_outline_label" translate_by="gemini">アウトライン：</string>
    <string name="literature_outline_introduction_title" translate_by="gpt">1. はじめに</string>
    <string name="literature_outline_introduction_context" translate_by="gpt">文学作品とその文脈を紹介します。</string>
    <string name="literature_outline_introduction_author" translate_by="gpt">著者と選択した分析の焦点に対する関連性を述べてください。</string>
    <string name="literature_outline_background_title" translate_by="gpt">2. 背景</string>
    <string name="literature_outline_background_summary" translate_by="gemini">プロットまたは主要な登場人物の要約（分析タイプによる）</string>
    <string name="literature_outline_background_context" translate_by="gemini">より深い分析に必要なコンテキストを提供します。</string>
    <string name="literature_outline_analysis_title" translate_by="gpt">3. メイン分析</string>
    <string name="literature_outline_analysis_deep_dive" translate_by="gemini">ディープダイブ：%1$s</string>
    <string name="literature_outline_analysis_evidence" translate_by="gemini">テキストから証拠（引用、出来事、象徴など）を使ってください。</string>
    <string name="literature_outline_connections_title" translate_by="gpt">4. 接続</string>
    <string name="literature_outline_connections_themes" translate_by="gpt">大きなテーマや現実の影響へのリンク分析。</string>
    <string name="literature_outline_connections_contrast" translate_by="gpt">他のキャラクターや作品と対比することもできます。</string>
    <string name="literature_outline_conclusion_title" translate_by="gpt">5. 結論</string>
    <string name="literature_outline_conclusion_insights" translate_by="gemini">主な洞察を再提示する</string>
    <string name="literature_outline_conclusion_value" translate_by="gemini">学術的な視点から、その仕事の価値を考察する。</string>
</resources>