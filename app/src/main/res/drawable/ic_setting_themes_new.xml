<vector xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:aapt="http://schemas.android.com/aapt"
    android:width="32dp"
    android:height="32dp"
    android:viewportWidth="32"
    android:viewportHeight="32">
  <path
      android:pathData="M8,0L24,0A8,8 0,0 1,32 8L32,24A8,8 0,0 1,24 32L8,32A8,8 0,0 1,0 24L0,8A8,8 0,0 1,8 0z">
    <aapt:attr name="android:fillColor">
      <gradient 
          android:startX="20.689"
          android:startY="-14.465"
          android:endX="-8.242"
          android:endY="14.318"
          android:type="linear">
        <item android:offset="0" android:color="#FFF67FAE"/>
        <item android:offset="1" android:color="#FFAC70D4"/>
      </gradient>
    </aapt:attr>
  </path>
  <group>
    <clip-path
        android:pathData="M6,6h20v20h-20z"/>
    <path
        android:pathData="M16,6C10.478,6 6,10.478 6,16C6,21.522 10.478,26 16,26C16.922,26 17.667,25.256 17.667,24.333C17.667,23.9 17.506,23.511 17.233,23.217C16.972,22.922 16.817,22.539 16.817,22.111C16.817,21.189 17.561,20.444 18.483,20.444H20.444C23.511,20.444 26,17.955 26,14.889C26,9.978 21.522,6 16,6ZM9.889,16C8.967,16 8.222,15.256 8.222,14.333C8.222,13.411 8.967,12.667 9.889,12.667C10.811,12.667 11.556,13.411 11.556,14.333C11.556,15.256 10.811,16 9.889,16ZM13.222,11.556C12.3,11.556 11.556,10.811 11.556,9.889C11.556,8.967 12.3,8.222 13.222,8.222C14.144,8.222 14.889,8.967 14.889,9.889C14.889,10.811 14.144,11.556 13.222,11.556ZM18.778,11.556C17.855,11.556 17.111,10.811 17.111,9.889C17.111,8.967 17.855,8.222 18.778,8.222C19.7,8.222 20.444,8.967 20.444,9.889C20.444,10.811 19.7,11.556 18.778,11.556ZM22.111,16C21.189,16 20.444,15.256 20.444,14.333C20.444,13.411 21.189,12.667 22.111,12.667C23.033,12.667 23.778,13.411 23.778,14.333C23.778,15.256 23.033,16 22.111,16Z"
        android:fillColor="#ffffff"/>
  </group>
</vector>
