<?xml version='1.0' encoding='utf-8'?>
<resources>
    <string name="generate_outline" translate_by="gemini">Generer disposisjon</string>
    <string name="placeholder_default" translate_by="gpt">Skriv inn teksten din her…</string>
    <string name="essay_screen_description" translate_by="gpt">Selvfølgelig! Jeg kan hjelpe med det. Vennligst gi noen detaljer for essayet ditt nedenfor.</string>
    <string name="label_choose_topic" translate_by="gpt">Velg et emne</string>
    <string name="label_essay_type" translate_by="gemini">Type essay</string>
    <string name="label_word_count" translate_by="gemini">Ordtelling</string>
    <string name="label_language_tone" translate_by="gpt">Språk + tone</string>
    <string name="placeholder_topic" translate_by="google">Eks: Beskriv et sted som får deg til å føle deg i fred</string>
    <string name="placeholder_essay_type" translate_by="gpt">Eksempel: Argumenterende, Fortellende…</string>
    <string name="placeholder_word_count" translate_by="gemini">Eks: 300 ord, 500 ord, 1000 ord…</string>
    <string name="placeholder_language_tone" translate_by="gpt">Eksempel: Formell, akademisk, …</string>
    <string name="research_screen_description" translate_by="gpt">Et sted å omdanne rådata til meningsfulle, visuelle historier gjennom forskning og analyse.</string>
    <string name="label_research_topic" translate_by="gpt">Forskningsområde</string>
    <string name="label_research_goal" translate_by="gpt">Forskningsmål</string>
    <string name="label_preferred_sources" translate_by="gpt">Foretrukne kilder</string>
    <string name="label_depth_length" translate_by="gpt">Dybde / Lengde</string>
    <string name="label_academic_level" translate_by="gemini">Utdanningsnivå</string>
    <string name="placeholder_research_topic" translate_by="gpt">Klimaendringer, Innvirkningen av AI på jobber, …</string>
    <string name="placeholder_research_goal" translate_by="gpt">Eksempel: Informasjonsinnhenting, Trendanalyse…</string>
    <string name="placeholder_preferred_sources" translate_by="google">Eks: Vitenskapelige tidsskrifter, bøker, offisielle artikler</string>
    <string name="placeholder_depth_length" translate_by="gemini">Eks: 300 ord, 500 ord, 1000 ord…</string>
    <string name="placeholder_academic_level" translate_by="gpt">Videregående elever, universitetsstudenter, avansert forskning, …</string>
    <string name="literature_screen_description" translate_by="gemini">Fra ord til skjulte betydninger – vi hjelper deg med å avdekke den sanne verdien av hvert litterære verk.</string>
    <string name="label_title_of_work" translate_by="gpt">Tittel på verket</string>
    <string name="label_author" translate_by="gpt">Forfatter</string>
    <string name="label_analysis_type" translate_by="gpt">Hva vil du analysere?</string>
    <string name="label_format" translate_by="gpt">Lengde / format</string>
    <string name="placeholder_title" translate_by="gpt">Den store Gatsby</string>
    <string name="placeholder_author" translate_by="gpt">F. Scott Fitzgerald</string>
    <string name="placeholder_analysis_type" translate_by="google">Eks: Karakteranalyse, hovedtemaer …</string>
    <string name="placeholder_format" translate_by="gemini">Eks: 300 ord, 500 ord, 1000 ord…</string>
    <string name="placeholder_academic_level_literature" translate_by="gpt">Ungdomsskole, videregående skole eller universitet, …</string>
    <string name="research_outline_topic_label" translate_by="gpt">📘 Forskningsområde: %1$s</string>
    <string name="research_outline_goal_label" translate_by="gpt">🎯 Forskningsmål: %1$s</string>
    <string name="research_outline_sources_label" translate_by="gpt">📚 Foretrukne kilder: %1$s</string>
    <string name="research_outline_depth_label" translate_by="gpt">📏 Dybde/Lengde: %1$s</string>
    <string name="research_outline_academic_level_label" translate_by="gpt">🎓 Akademisk nivå: %1$s</string>
    <string name="research_outline_suggested_outline_label" translate_by="gpt">🧾 Foreslått disposisjon:</string>
    <string name="research_outline_introduction_title" translate_by="gpt">1. Introduksjon</string>
    <string name="research_outline_introduction_overview" translate_by="gpt">- Kort oversikt over %1$s</string>
    <string name="research_outline_introduction_importance" translate_by="gemini">Viktigheten av forskning på %1$s-nivå</string>
    <string name="research_outline_objectives_title" translate_by="gpt">2. Mål</string>
    <string name="research_outline_objectives_goal" translate_by="gemini">- Tydeliggjør hovedmålet: %1$s</string>
    <string name="research_outline_methodology_title" translate_by="gpt">3. Metodikk</string>
    <string name="research_outline_methodology_approach" translate_by="google">- Forskningstilnærming</string>
    <string name="research_outline_methodology_sources" translate_by="gpt">- Datakilder: %1$s</string>
    <string name="research_outline_key_insights_title" translate_by="gpt">4. Nøkkelinnsikter</string>
    <string name="research_outline_key_insights_trends" translate_by="gpt">- Diskuter trender, fakta eller analysefunn</string>
    <string name="research_outline_key_insights_citations" translate_by="gemini">Bruk sitater om nødvendig</string>
    <string name="research_outline_conclusion_title" translate_by="gpt">5. Konklusjon</string>
    <string name="research_outline_conclusion_summary" translate_by="gemini">Oppsummering av funn</string>
    <string name="research_outline_conclusion_implications" translate_by="gpt">- Implikasjoner eller fremtidig arbeid</string>
    <string name="essay_outline_topic_label" translate_by="gemini">✏️ Essayemne: %1$s</string>
    <string name="essay_outline_type_label" translate_by="gpt">📝 Essay Type: %1$s</string>
    <string name="essay_outline_word_count_label" translate_by="gpt">🔢 Antall ord: %1$s</string>
    <string name="essay_outline_language_tone_label" translate_by="gpt">🗣️ Språk og Tone: %1$s</string>
    <string name="essay_outline_suggested_outline_label" translate_by="gpt">🧾 Foreslått disposisjon:</string>
    <string name="essay_outline_introduction_title" translate_by="gpt">1. Introduksjon</string>
    <string name="essay_outline_introduction_topic" translate_by="gemini">– Introduser emnet: %1$s</string>
    <string name="essay_outline_introduction_background" translate_by="gpt">- Gi bakgrunn/kontekst</string>
    <string name="essay_outline_introduction_thesis" translate_by="gemini">Angi tesen</string>
    <string name="essay_outline_body_title" translate_by="gemini">2. Avsnitt i hovedteksten</string>
    <string name="essay_outline_body_paragraph1" translate_by="gemini">Avsnitt 1: Første argument eller poeng</string>
    <string name="essay_outline_body_paragraph2" translate_by="gpt">- Avsnitt 2: Støttende bevis eller narrativ</string>
    <string name="essay_outline_body_paragraph3" translate_by="gemini">Avsnitt 3: Motargument eller ytterligere detalj</string>
    <string name="essay_outline_conclusion_title" translate_by="gpt">3. Konklusjon</string>
    <string name="essay_outline_conclusion_summary" translate_by="gpt">- Oppsummer hovedpunktene</string>
    <string name="essay_outline_conclusion_restate" translate_by="gpt">- Formuler oppgaven på nytt på en annen måte</string>
    <string name="essay_outline_conclusion_final" translate_by="gpt">Avslutt med en sterk avsluttende tanke</string>
    <string name="essay_outline_notes_title" translate_by="gpt">✨ Notater:</string>
    <string name="essay_outline_notes_tone" translate_by="gemini">Oppretthold en %1$s tone gjennomgående</string>
    <string name="essay_outline_notes_wordcount" translate_by="gemini">Sikt etter omtrent %1$s totalt</string>
    <string name="essay_outline_notes_structure" translate_by="gpt">- Følg typisk %1$s essaystruktur</string>
    <string name="literature_outline_title_label" translate_by="gpt">Tittel: %1$s</string>
    <string name="literature_outline_author_label" translate_by="gpt">Forfatter: %1$s</string>
    <string name="literature_outline_focus_label" translate_by="gpt">Fokus: %1$s</string>
    <string name="literature_outline_length_label" translate_by="gpt">Lengde: %1$s</string>
    <string name="literature_outline_academic_level_label" translate_by="gpt">Akademisk nivå: %1$s</string>
    <string name="literature_outline_outline_label" translate_by="gpt">Oversikt:</string>
    <string name="literature_outline_introduction_title" translate_by="gpt">1. Introduksjon</string>
    <string name="literature_outline_introduction_context" translate_by="gemini">Introduser det litterære verket og dets kontekst.</string>
    <string name="literature_outline_introduction_author" translate_by="gemini">Nevn forfatteren og relevansen for det valgte analysefokuset.</string>
    <string name="literature_outline_background_title" translate_by="gpt">2. Bakgrunn</string>
    <string name="literature_outline_background_summary" translate_by="gemini">Sammendrag av handlingen eller nøkkelkarakterer (avhengig av analysetype).</string>
    <string name="literature_outline_background_context" translate_by="gpt">Gi nødvendig kontekst for dypere analyse.</string>
    <string name="literature_outline_analysis_title" translate_by="gpt">3. Hovedanalyse</string>
    <string name="literature_outline_analysis_deep_dive" translate_by="gpt">Dypdykk i: %1$s.</string>
    <string name="literature_outline_analysis_evidence" translate_by="gpt">Bruk bevis fra teksten: sitater, hendelser, symbolikk, osv.</string>
    <string name="literature_outline_connections_title" translate_by="gpt">4. Tilkoblinger</string>
    <string name="literature_outline_connections_themes" translate_by="gemini">Koble analysen til større temaer eller implikasjoner i den virkelige verden.</string>
    <string name="literature_outline_connections_contrast" translate_by="gpt">Valgfritt å sammenligne med andre karakterer eller verk.</string>
    <string name="literature_outline_conclusion_title" translate_by="gpt">5. Konklusjon</string>
    <string name="literature_outline_conclusion_insights" translate_by="gemini">Gjenta viktige innsikter.</string>
    <string name="literature_outline_conclusion_value" translate_by="gpt">Reflekter over verdien av arbeidet fra et akademisk perspektiv.</string>
</resources>