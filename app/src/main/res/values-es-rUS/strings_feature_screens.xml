<?xml version='1.0' encoding='utf-8'?>
<resources>
    <string name="generate_outline" translate_by="gpt">Generar Esquema</string>
    <string name="placeholder_default" translate_by="gpt">Ingresa tu texto aquí…</string>
    <string name="essay_screen_description" translate_by="gemini">¡Claro! Puedo ayudarte con eso. Por favor, proporciona algunos detalles para tu ensayo a continuación.</string>
    <string name="label_choose_topic" translate_by="gpt">Elige un tema</string>
    <string name="label_essay_type" translate_by="gpt">Tipo de ensayo</string>
    <string name="label_word_count" translate_by="gpt">Recuento de palabras</string>
    <string name="label_language_tone" translate_by="gpt">Idioma + tono</string>
    <string name="placeholder_topic" translate_by="gpt">Describe un lugar que te haga sentir en paz</string>
    <string name="placeholder_essay_type" translate_by="gpt">Ej: Argumentativo, Narrativo…</string>
    <string name="placeholder_word_count" translate_by="gpt">Ej: 300 palabras, 500 palabras, 1000 palabras…</string>
    <string name="placeholder_language_tone" translate_by="gpt">Ej: Formal, académico, …</string>
    <string name="research_screen_description" translate_by="gemini">Un lugar para convertir datos brutos en historias visuales significativas a través de la investigación y el análisis.</string>
    <string name="label_research_topic" translate_by="gpt">Tema de investigación</string>
    <string name="label_research_goal" translate_by="gpt">Objetivo de investigación</string>
    <string name="label_preferred_sources" translate_by="gpt">Fuentes preferidas</string>
    <string name="label_depth_length" translate_by="gpt">Profundidad / Longitud</string>
    <string name="label_academic_level" translate_by="gpt">Nivel académico</string>
    <string name="placeholder_research_topic" translate_by="gemini">Cambio climático, El impacto de la IA en el empleo, …</string>
    <string name="placeholder_research_goal" translate_by="google">Ej: recopilación de información, análisis de tendencias …</string>
    <string name="placeholder_preferred_sources" translate_by="gemini">Revistas científicas, libros, artículos oficiales</string>
    <string name="placeholder_depth_length" translate_by="gpt">Ej: 300 palabras, 500 palabras, 1000 palabras…</string>
    <string name="placeholder_academic_level" translate_by="google">Ej: Estudiantes de secundaria, estudiantes universitarios, investigación avanzada, …</string>
    <string name="literature_screen_description" translate_by="gpt">Desde palabras hasta significados ocultos, te ayudamos a descubrir el verdadero valor de cada obra literaria.</string>
    <string name="label_title_of_work" translate_by="gemini">Título del trabajo</string>
    <string name="label_author" translate_by="gpt">Autor</string>
    <string name="label_analysis_type" translate_by="gpt">¿Qué quieres analizar?</string>
    <string name="label_format" translate_by="gpt">Longitud / formato</string>
    <string name="placeholder_title" translate_by="gpt">Ej: El Gran Gatsby</string>
    <string name="placeholder_author" translate_by="google">Ej: F. Scott Fitzgerald</string>
    <string name="placeholder_analysis_type" translate_by="gpt">Análisis de personajes, Temas principales…</string>
    <string name="placeholder_format" translate_by="gpt">Ej: 300 palabras, 500 palabras, 1000 palabras…</string>
    <string name="placeholder_academic_level_literature" translate_by="gemini">Escuela intermedia, escuela secundaria o universidad, …</string>
    <string name="research_outline_topic_label" translate_by="gpt">📘 Tema de investigación: %1$s</string>
    <string name="research_outline_goal_label" translate_by="gpt">🎯 Objetivo de Investigación: %1$s</string>
    <string name="research_outline_sources_label" translate_by="gpt">📚 Fuentes Preferidas: %1$s</string>
    <string name="research_outline_depth_label" translate_by="gemini">📏 Profundidad/Longitud: %1$s</string>
    <string name="research_outline_academic_level_label" translate_by="gpt">🎓 Nivel Académico: %1$s</string>
    <string name="research_outline_suggested_outline_label" translate_by="gpt">🧾 Esquema Sugerido:</string>
    <string name="research_outline_introduction_title" translate_by="gpt">1. Introducción</string>
    <string name="research_outline_introduction_overview" translate_by="gemini">Breve resumen de %1$s</string>
    <string name="research_outline_introduction_importance" translate_by="gemini">Importancia de la investigación a nivel de %1$s</string>
    <string name="research_outline_objectives_title" translate_by="gpt">2. Objetivos</string>
    <string name="research_outline_objectives_goal" translate_by="gemini">Aclara el objetivo principal: %1$s</string>
    <string name="research_outline_methodology_title" translate_by="gpt">3. Metodología</string>
    <string name="research_outline_methodology_approach" translate_by="gpt">- Enfoque de investigación</string>
    <string name="research_outline_methodology_sources" translate_by="gpt">- Fuentes de datos: %1$s</string>
    <string name="research_outline_key_insights_title" translate_by="google">4. Insights clave</string>
    <string name="research_outline_key_insights_trends" translate_by="gpt">- Discutir tendencias, hechos o hallazgos de análisis</string>
    <string name="research_outline_key_insights_citations" translate_by="google">- Use citas si es necesario</string>
    <string name="research_outline_conclusion_title" translate_by="gpt">5. Conclusión</string>
    <string name="research_outline_conclusion_summary" translate_by="gpt">Resumen de hallazgos</string>
    <string name="research_outline_conclusion_implications" translate_by="gpt">- Implicaciones o trabajo futuro</string>
    <string name="essay_outline_topic_label" translate_by="gpt">✏️ Tema del ensayo: %1$s</string>
    <string name="essay_outline_type_label" translate_by="gpt">📝 Tipo de ensayo: %1$s</string>
    <string name="essay_outline_word_count_label" translate_by="gemini">🔢 Conteo de palabras: %1$s</string>
    <string name="essay_outline_language_tone_label" translate_by="gpt">🗣️ Idioma y Tono: %1$s</string>
    <string name="essay_outline_suggested_outline_label" translate_by="gpt">🧾 Esquema Sugerido:</string>
    <string name="essay_outline_introduction_title" translate_by="gpt">1. Introducción</string>
    <string name="essay_outline_introduction_topic" translate_by="gemini">Introduce el tema: %1$s</string>
    <string name="essay_outline_introduction_background" translate_by="gpt">- Proporcionar antecedentes/contexto</string>
    <string name="essay_outline_introduction_thesis" translate_by="gemini">Indique la tesis</string>
    <string name="essay_outline_body_title" translate_by="gpt">2. Párrafos del cuerpo</string>
    <string name="essay_outline_body_paragraph1" translate_by="gpt">- Párrafo 1: Primer argumento o punto</string>
    <string name="essay_outline_body_paragraph2" translate_by="gpt">- Párrafo 2: Evidencia de apoyo o narrativa</string>
    <string name="essay_outline_body_paragraph3" translate_by="gemini">Párrafo 3: Contraargumento o detalle adicional</string>
    <string name="essay_outline_conclusion_title" translate_by="gpt">3. Conclusión</string>
    <string name="essay_outline_conclusion_summary" translate_by="gpt">- Resumir puntos clave</string>
    <string name="essay_outline_conclusion_restate" translate_by="gpt">- Reformula la tesis de una nueva manera</string>
    <string name="essay_outline_conclusion_final" translate_by="gemini">Concluye con una reflexión final impactante</string>
    <string name="essay_outline_notes_title" translate_by="gpt">✨ Notas:</string>
    <string name="essay_outline_notes_tone" translate_by="gemini">Mantén un tono %1$s en todo momento</string>
    <string name="essay_outline_notes_wordcount" translate_by="gemini">Apunta a aproximadamente %1$s en total</string>
    <string name="essay_outline_notes_structure" translate_by="gpt">- Sigue la estructura típica del ensayo %1$s</string>
    <string name="literature_outline_title_label" translate_by="gpt">Título: %1$s</string>
    <string name="literature_outline_author_label" translate_by="gpt">Autor: %1$s</string>
    <string name="literature_outline_focus_label" translate_by="gpt">Enfoque: %1$s</string>
    <string name="literature_outline_length_label" translate_by="gpt">Longitud: %1$s</string>
    <string name="literature_outline_academic_level_label" translate_by="gpt">Nivel Académico: %1$s</string>
    <string name="literature_outline_outline_label" translate_by="gpt">Esquema:</string>
    <string name="literature_outline_introduction_title" translate_by="gpt">1. Introducción</string>
    <string name="literature_outline_introduction_context" translate_by="gemini">Presenta la obra literaria y su contexto.</string>
    <string name="literature_outline_introduction_author" translate_by="gpt">Menciona al autor y la relevancia para el enfoque de análisis elegido.</string>
    <string name="literature_outline_background_title" translate_by="gpt">2. Fondo</string>
    <string name="literature_outline_background_summary" translate_by="gemini">Resumen del argumento o personajes clave (según el tipo de análisis).</string>
    <string name="literature_outline_background_context" translate_by="gpt">Proporcione el contexto necesario para un análisis más profundo.</string>
    <string name="literature_outline_analysis_title" translate_by="gpt">3. Análisis Principal</string>
    <string name="literature_outline_analysis_deep_dive" translate_by="gpt">Profundiza en: %1$s.</string>
    <string name="literature_outline_analysis_evidence" translate_by="gpt">Usa evidencia del texto: citas, eventos, simbolismo, etc.</string>
    <string name="literature_outline_connections_title" translate_by="gpt">4. Conexiones</string>
    <string name="literature_outline_connections_themes" translate_by="gemini">Vincular el análisis a temas más amplios o implicaciones del mundo real.</string>
    <string name="literature_outline_connections_contrast" translate_by="gpt">Opcionalmente, contrasta con otros personajes u obras.</string>
    <string name="literature_outline_conclusion_title" translate_by="gpt">5. Conclusión</string>
    <string name="literature_outline_conclusion_insights" translate_by="gpt">Reformular los puntos clave.</string>
    <string name="literature_outline_conclusion_value" translate_by="gpt">Reflexiona sobre el valor del trabajo desde una perspectiva académica.</string>
</resources>