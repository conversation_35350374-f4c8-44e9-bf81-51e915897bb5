<?xml version="1.0" encoding="utf-8"?>
<ripple xmlns:android="http://schemas.android.com/apk/res/android"
    android:color="@color/clr_medium_grey">
    <item android:id="@android:id/mask">
        <shape android:shape="rectangle">
            <solid android:color="@color/clr_medium_grey" />
            <corners android:radius="@dimen/dimen_radius_corner" />
        </shape>
    </item>
    <!--    <item>-->
    <!--        <shape>-->
    <!--            <gradient-->
    <!--                android:angle="45"-->
    <!--                android:startColor="#CCFFFFFF"-->
    <!--                android:endColor="#33FFFFFF"-->
    <!--                android:type="linear" />-->
    <!--            <corners android:radius="@dimen/dimen_radius_corner" />-->
    <!--        </shape>-->
    <!--    </item>-->
    <item android:id="@android:id/background">
        <!--        android:bottom="1.5dp"-->
        <!--        android:left="1.5dp"-->
        <!--        android:right="1.5dp"-->
        <!--        android:top="1.5dp">-->
        <shape android:shape="rectangle">
            <!--            <solid android:color="#00C6F4" />-->
            <solid android:color="#1482E7" />
            <corners android:radius="@dimen/dimen_radius_corner" />
        </shape>
    </item>
</ripple>