package amobi.module.compose.extentions

import androidx.compose.ui.graphics.Color
import kotlin.math.max
import kotlin.math.min

fun Color.withAlpha(alpha: Float): Color = this.copy(alpha = alpha)

fun Color.darkMode(percent: Float = 0.3f): Color {
    val hsl = FloatArray(3)
    android.graphics.Color.RGBToHSV(
        (this.red * 255).toInt(),
        (this.green * 255).toInt(),
        (this.blue * 255).toInt(),
        hsl,
    )
    hsl[2] = max(0f, hsl[2] - percent) // Decrease lightness for dark mode
    val darkColor = android.graphics.Color.HSVToColor(hsl)
    return Color(darkColor)
}

fun Color.lightMode(percent: Float = 0.3f): Color {
    val hsl = FloatArray(3)
    android.graphics.Color.RGBToHSV(
        (this.red * 255).toInt(),
        (this.green * 255).toInt(),
        (this.blue * 255).toInt(),
        hsl,
    )
    hsl[2] = min(1f, hsl[2] + percent) // Increase lightness for light mode
    val lightColor = android.graphics.Color.HSVToColor(hsl)
    return Color(lightColor)
}
