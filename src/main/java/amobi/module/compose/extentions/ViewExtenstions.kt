package amobi.module.compose.extentions

import amobi.module.common.utils.MixedUtils
import android.graphics.Bitmap
import android.graphics.Canvas
import android.graphics.drawable.BitmapDrawable
import android.graphics.drawable.Drawable
import android.view.View
import androidx.core.graphics.createBitmap

fun View.toDrawable(
    width: Int,
    height: Int,
): Drawable {
    measure(
        View.MeasureSpec.makeMeasureSpec(0, View.MeasureSpec.UNSPECIFIED),
        View.MeasureSpec.makeMeasureSpec(0, View.MeasureSpec.UNSPECIFIED),
    )
    layout(0, 0, MixedUtils.dp2px(width.toFloat()), MixedUtils.dp2px(height.toFloat()))
    val bitmap = createBitmap(MixedUtils.dp2px(width.toFloat()), MixedUtils.dp2px(height.toFloat()))
    val canvas = Canvas(bitmap)
    draw(canvas)
    return BitmapDrawable(resources, bitmap)
}
