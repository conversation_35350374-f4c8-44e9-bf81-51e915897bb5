package amobi.module.compose.extentions

import amobi.module.compose.theme.AppColors
import androidx.compose.foundation.Indication
import androidx.compose.foundation.clickable
import androidx.compose.foundation.interaction.MutableInteractionSource
import androidx.compose.foundation.layout.defaultMinSize
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material3.ripple
import androidx.compose.runtime.Composable
import androidx.compose.runtime.remember
import androidx.compose.ui.Modifier
import androidx.compose.ui.composed
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.unit.Dp
import androidx.compose.ui.unit.dp

fun Modifier.conditional(
    condition: Bo<PERSON>an,
    modifier: Modifier.() -> Modifier,
): Modifier = if (condition) then(modifier(Modifier)) else this

fun Modifier.paddingHorizontal(horizontalPadding: Dp): Modifier = padding(horizontal = horizontalPadding)

fun Modifier.paddingVertical(verticalPadding: Dp): Modifier = padding(vertical = verticalPadding)

fun Modifier.minWidth(minWidth: Dp): Modifier = defaultMinSize(minWidth = minWidth)

fun Modifier.minHeight(minHeight: Dp): Modifier = defaultMinSize(minHeight = minHeight)

fun Modifier.minSize(minSize: Dp): Modifier = defaultMinSize(minWidth = minSize, minHeight = minSize)

fun Modifier.clipCorner(cornerRadius: Dp): Modifier = clip(shape = RoundedCornerShape(cornerRadius))

fun Modifier.appClickableNoRipple(onClick: () -> Unit) =
    composed {
        clickable(
            interactionSource = remember { MutableInteractionSource() },
            indication = null,
            onClick = onClick,
        )
    }

@Composable
fun appRipple(
    bounded: Boolean = true,
    color: Color = AppColors.current.ripple,
) = ripple(
    bounded = bounded,
    color = color,
)

fun Modifier.appClickable(
    rippleCorner: Dp = 8.dp,
    rippleIndication: Indication? = null,
    enabled: Boolean = true,
    onClick: () -> Unit,
) = composed {
    clipCorner(rippleCorner)
        .clickable(
            interactionSource = remember { MutableInteractionSource() },
            indication = rippleIndication ?: appRipple(),
            enabled = enabled,
            onClick = onClick,
        )
}
