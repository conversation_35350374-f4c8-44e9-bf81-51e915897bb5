package amobi.module.compose.foundation

import android.content.Context
import android.content.res.Configuration
import android.graphics.drawable.Drawable
import android.os.Build
import android.view.ViewGroup
import android.widget.ImageView
import androidx.appcompat.widget.AppCompatImageView
import androidx.compose.foundation.background
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableIntStateOf
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.setValue
import androidx.compose.runtime.snapshotFlow
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.platform.LocalConfiguration
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.viewinterop.AndroidView
import androidx.lifecycle.Lifecycle
import androidx.lifecycle.compose.LifecycleEventEffect
import androidx.vectordrawable.graphics.drawable.Animatable2Compat
import com.bumptech.glide.Glide
import com.bumptech.glide.load.DataSource
import com.bumptech.glide.load.engine.GlideException
import com.bumptech.glide.load.resource.gif.GifDrawable
import com.bumptech.glide.request.RequestListener
import com.bumptech.glide.request.target.Target

@Composable
fun GifBasedOnGlideCompose(
    modifier: Modifier = Modifier,
    imageSource: Any,
    backgroundColor: Color = Color.Transparent,
    isLoop: Boolean = true,
    onGifAnimationEnd: (() -> Unit)? = null,
) {
    val configuration = LocalConfiguration.current

    val context = LocalContext.current

    var isViewInited by remember { mutableStateOf(false) }

    var imgViewLegacy by remember { mutableStateOf<AppCompatImageView?>(null) }

    var orientation by remember {
        mutableIntStateOf(Configuration.ORIENTATION_PORTRAIT)
    }

    LaunchedEffect(configuration) {
        snapshotFlow { configuration.orientation }
            .collect {
                orientation = it

                imgViewLegacy?.let { imgView ->
                    imgView.invalidate()
                    setGifImage(
                        context,
                        imgView,
                        imageSource,
                        isLoop,
                        onGifAnimationEnd,
                    )
                }
            }
    }

    LifecycleEventEffect(event = Lifecycle.Event.ON_START) {
        if (Build.VERSION.SDK_INT < Build.VERSION_CODES.N) {
            imgViewLegacy?.let {
                it.invalidate()
                setGifImage(
                    context,
                    it,
                    imageSource,
                    isLoop,
                    onGifAnimationEnd,
                )
            }
        }
    }

    AndroidView(
        modifier =
            modifier
                .background(backgroundColor),
        factory = { ctx ->
            AppCompatImageView(ctx)
                .apply {
                    minimumWidth = 0
                    minimumHeight = 0
                    layoutParams =
                        ViewGroup.LayoutParams(
                            ViewGroup.LayoutParams.MATCH_PARENT,
                            ViewGroup.LayoutParams.MATCH_PARENT,
                        )
                    scaleType = ImageView.ScaleType.FIT_CENTER
                }.also { imgViewLegacy = it }
        },
        update = {
            if (!isViewInited) {
                setGifImage(
                    context,
                    it,
                    imageSource,
                    isLoop,
                    onGifAnimationEnd,
                )

                isViewInited = true
            }
        },
    )
}

private fun setGifImage(
    context: Context,
    imgView: AppCompatImageView,
    imageSource: Any,
    isLoop: Boolean,
    onGifAnimationEnd: (() -> Unit)? = null,
) {
    Glide
        .with(context)
        .asGif()
        .load(imageSource)
        .addListener(
            object : RequestListener<GifDrawable> {
                override fun onLoadFailed(
                    e: GlideException?,
                    model: Any?,
                    target: Target<GifDrawable>,
                    isFirstResource: Boolean,
                ): Boolean = false

                override fun onResourceReady(
                    resource: GifDrawable,
                    model: Any,
                    target: Target<GifDrawable>?,
                    dataSource: DataSource,
                    isFirstResource: Boolean,
                ): Boolean {
                    if (!isLoop)
                        resource.setLoopCount(1)

                    if (onGifAnimationEnd != null)
                        resource.registerAnimationCallback(
                            object :
                                Animatable2Compat.AnimationCallback() {
                                override fun onAnimationEnd(drawable: Drawable?) {
                                    super.onAnimationEnd(drawable)

                                    onGifAnimationEnd.invoke()
                                }
                            },
                        )

                    return false
                }
            },
        ).into(imgView)
}
