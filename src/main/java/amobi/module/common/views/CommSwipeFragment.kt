package amobi.module.common.views

import amobi.module.common.R
import amobi.module.common.utils.repeatOnceWhenResumed
import amobi.module.common.utils.repeatOnceWhenStarted
import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.LinearLayout
import androidx.fragment.app.Fragment
import androidx.viewbinding.ViewBinding
import androidx.viewpager.widget.PagerAdapter
import androidx.viewpager.widget.ViewPager
import kotlinx.coroutines.delay
import java.lang.ref.WeakReference

abstract class CommSwipeFragment<VB : ViewBinding>(
    private val bindingInflate: BindingInflate<VB>,
) : CommFragment() {
    protected var rootBinding: VB? = null
    protected val binding get() = rootBinding!!

    protected var commSwipeViewPager: CommSwipeViewPager? = null
    protected var previousFragment: CommSwipeFragment<*>? = null
    protected var previousFragView: View? = null
    protected var baseFrag: View? = null
    protected var isDoneAnimation = false

    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?,
    ): View? {
        baseFrag = layoutInflater.inflate(R.layout.comm_swipe_frmt, container, false)
        val adapter = WizardPagerAdapter(WeakReference(baseFrag))
        commSwipeViewPager = baseFrag!!.findViewById<View>(R.id.pager) as CommSwipeViewPager?
        commSwipeViewPager!!.setDurationScroll(300)
        commSwipeViewPager!!.setPagingEnabled(false)
        commSwipeViewPager!!.adapter = adapter
        val activity = activity ?: return baseFrag
        val fragments: List<Fragment> = activity.supportFragmentManager.fragments
        commSwipeViewPager!!.currentItem = 0
        var counter = 0
        for (i in fragments.indices.reversed()) {
            val frag: Fragment = fragments[i]
            if (this === frag) continue
            if (frag is CommSwipeFragment<*>) {
                if (counter == 0) {
                    previousFragment = frag
                    previousFragView = frag.view
                }
                counter++
            }
        }
        if (counter <= 0) {
            commSwipeViewPager!!.setPagingEnabled(false)
        }

        previousFragment?.onHideFragment()
        <EMAIL>()

        if (previousFragView == null) {
            commSwipeViewPager?.setCurrentItem(1, false)
            this.repeatOnceWhenResumed {
                delay(12)
                if (commSwipeViewPager?.currentItem == 1) return@repeatOnceWhenResumed
                commSwipeViewPager?.setCurrentItem(1, false)
            }
        } else {
            this.repeatOnceWhenStarted {
                delay(12)
                commSwipeViewPager?.setCurrentItem(1, true)
                commSwipeViewPager?.addOnPageChangeListener(
                    object : ViewPager.OnPageChangeListener {
                        override fun onPageScrollStateChanged(state: Int) {}

                        override fun onPageSelected(position: Int) {}

                        override fun onPageScrolled(
                            position: Int,
                            positionOffset: Float,
                            positionOffsetPixels: Int,
                        ) {
                            if (position == 0 && positionOffset == 0f) {
                                destroyFragment()
                            }
                            if (position == 1) {
                                isDoneAnimation = true
                            }
                            if (previousFragView == null) return
                            if (position == 1) {
                                previousFragView!!.alpha = 0f
                                previousFragView!!.visibility = View.GONE
                            } else {
                                previousFragView!!.x = -200 * (positionOffset)
                                previousFragView!!.alpha = 1 - positionOffset
                                previousFragView!!.visibility = View.VISIBLE
                            }
                        }
                    },
                )
            }
        }
        rootBinding = bindingInflate.invoke(inflater, container, false)

        val llytContentMain: LinearLayout = baseFrag!!.findViewById(R.id.contentMain)
        rootBinding!!.root.layoutParams =
            LinearLayout.LayoutParams(ViewGroup.LayoutParams.MATCH_PARENT, ViewGroup.LayoutParams.MATCH_PARENT)
        llytContentMain.addView(rootBinding!!.root)

        return baseFrag
    }

    open fun onFrontView() {}

    override fun onDestroyView() {
        super.onDestroyView()
        rootBinding = null
        isDestroyed = true
    }

    open fun goBack() {
        if (!isDoneAnimation) return
        val swipeViewPager = commSwipeViewPager ?: return
        if (swipeViewPager.currentItem == 0) {
            destroyFragment()
        } else {
            swipeViewPager.setCurrentItem(0, true)
        }
    }

    open fun onHideFragment() {}

    open fun onShowFragment() {}

    fun destroyFragment() {
        activity?.supportFragmentManager?.popBackStack()
    }

    internal inner class WizardPagerAdapter(
        private val weakFragView: WeakReference<View>,
    ) : PagerAdapter() {
        override fun instantiateItem(
            collection: View,
            position: Int,
        ): Any {
            var resId = 0
            when (position) {
                0 -> resId = R.id.transparentMain
                1 -> resId = R.id.contentMain
            }
            return weakFragView.get()?.findViewById(resId) ?: position
        }

        override fun getCount(): Int = 2

        override fun isViewFromObject(
            arg0: View,
            arg1: Any,
        ): Boolean = arg0 === (arg1 as View)
    }
}
