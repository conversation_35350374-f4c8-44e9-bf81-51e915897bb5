package amobi.module.common.advertisements.reward_ad

import amobi.module.common.advertisements.AdvertsConfig
import amobi.module.common.advertisements.AdvertsRequestStatus
import amobi.module.common.configs.CommFigs
import amobi.module.common.utils.DebugLogCustom
import amobi.module.common.views.CommActivity

object AdvertsManagerReward {
    var rewardAds: AdvertsInstanceReward? = null

    fun requestRewardAdverts(
        listAdsID: Array<String>,
        rewardAds: AdvertsInstanceReward? = null,
        onAdLoadedListener: ((Boolean) -> Unit?)? = null,
    ) {
        if (AdvertsConfig.instance.isHideAd) {
            onAdLoadedListener?.invoke(false)
            return
        }
        if (CommFigs.IS_SHOW_TEST_OPTION)
            DebugLogCustom.logd("requestRewardAdverts", CommFigs.LOG_TAG_REWARD_AD)

        if (rewardAds != null) {
            if (rewardAds.isAdvertsAvailable || rewardAds.isAdvertsLoading) {
                onAdLoadedListener?.invoke(false)
                return
            }
            rewardAds.requestRewardAd(onAdLoadedListener)
            return
        } else {
            if (this.rewardAds?.isAdvertsAvailable == true || this.rewardAds?.isAdvertsLoading == true) {
                onAdLoadedListener?.invoke(false)
                return
            }
            if (this.rewardAds == null)
                this.rewardAds = AdvertsInstanceReward(listAdsID)
            this.rewardAds?.requestRewardAd(onAdLoadedListener)
        }
    }

    fun showRewardAd(
        activity: CommActivity,
        rewardAds: AdvertsInstanceReward?,
        placement: String? = null,
        onUserEarnedReward: (() -> Unit)? = null,
        onShowAdCompleteListener: (() -> Unit?)? = null,
        onAdDismissedListener: (() -> Unit?)? = null,
    ): Boolean {
        if (rewardAds == null) {
            onShowAdCompleteListener?.invoke()
            return false
        }
        if (CommFigs.IS_SHOW_TEST_OPTION)
            DebugLogCustom.logd("checkRewardAdverts:" + rewardAds.isAdvertsAvailable, CommFigs.LOG_TAG_REWARD_AD)

        return rewardAds.showRewardAd(
            activity,
            placement = placement,
            onUserEarnedReward = onUserEarnedReward,
            onShowAdCompleteListener = onShowAdCompleteListener,
            onAdDismissedListener = onAdDismissedListener,
        )
    }

    fun checkRewardAdverts(rewardAds: AdvertsInstanceReward?): Boolean {
        val ads = rewardAds ?: return false
        return ads.isAdvertsAvailable
    }

    fun checkRewardAdvertsAvailableOrLoading(rewardAds: AdvertsInstanceReward?): Boolean {
        val ads = rewardAds ?: return false
        return ads.isAdvertsAvailable || ads.isAdvertsLoading
    }

    fun getRewardAdvertsRequestStatus(rewardAds: AdvertsInstanceReward?): AdvertsRequestStatus = rewardAds?.requestStatus ?: AdvertsRequestStatus.NOT_REQUEST
}
